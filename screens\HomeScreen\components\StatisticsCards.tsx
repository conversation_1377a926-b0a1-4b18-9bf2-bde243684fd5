import React from 'react';
import { View, Text, ScrollView } from 'react-native';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import { styles } from '../HomeScreen.styles';
import { UserData } from '../hooks/useUserData';

interface StatisticsCardsProps {
  userData: UserData;
}

export const StatisticsCards: React.FC<StatisticsCardsProps> = ({ userData }) => {
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={{ marginTop: 10 }}
    >
      <View style={styles.statCard}>
        <FontAwesome name="clock-o" size={28} color="#f39c12" />
        <Text style={styles.statNumber}>{userData.pendingTransactions}</Text>
        <Text style={styles.statLabel}>Chờ thanh toán</Text>
      </View>

      <View style={styles.statCard}>
        <FontAwesome name="calendar" size={28} color="#3498db" />
        <Text style={styles.statNumber}>{userData.transactionsToday}</Text>
        <Text style={styles.statLabel}>Giao dịch hôm nay</Text>
      </View>

      <View style={styles.statCard}>
        <FontAwesome name="calendar-check-o" size={28} color="#2ecc71" />
        <Text style={styles.statNumber}>{userData.transactionsYesterday}</Text>
        <Text style={styles.statLabel}>Hôm qua</Text>
      </View>

      <View style={styles.statCard}>
        <FontAwesome name="line-chart" size={28} color="#e74c3c" />
        <Text style={styles.statNumber}>{userData.transactionsThisMonth}</Text>
        <Text style={styles.statLabel}>Tháng này</Text>
      </View>
    </ScrollView>
  );
};
