import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image, ActivityIndicator, Dimensions } from 'react-native';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import { styles } from '../HomeScreen.styles';
import { BankAccount } from '../hooks/useBankAccounts';

interface BankAccountsListProps {
  linkedBanks: BankAccount[];
  loadingBankIds: number[];
  onToggleBankStatus: (bankId: number) => void;
}

export const BankAccountsList: React.FC<BankAccountsListProps> = ({
  linkedBanks,
  loadingBankIds,
  onToggleBankStatus,
}) => {
  const screenWidth = Dimensions.get('window').width;

  const getBadgeStyle = (type: string) => {
    switch (type.toLowerCase()) {
      case 'openapi':
        return { backgroundColor: '#f39c12' };
      case 'business':
        return { backgroundColor: '#2ecc71' };
      default:
        return { backgroundColor: '#3498db' };
    }
  };

  return (
    <View style={styles.section}>
      <View style={styles.sectionDivider}>
        <View style={styles.line} />
        <FontAwesome name="credit-card" size={16} color="#308a5a" />
        <Text style={styles.dividerText}> Tài khoản ngân hàng đã liên kết </Text>
        <View style={styles.line} />
      </View>
      
      {linkedBanks.length === 0 ? (
        <Text style={{ padding: 10, color: '#999' }}>Chưa có tài khoản nào</Text>
      ) : (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ paddingHorizontal: 16 }}
          snapToInterval={screenWidth - 80 + 12}
          decelerationRate="fast"
          snapToAlignment="start"
        >
          {linkedBanks.map((bank, idx) => (
            <View key={idx} style={styles.bankCard}>
              <TouchableOpacity
                onPress={() => onToggleBankStatus(bank.id)}
                disabled={loadingBankIds.includes(bank.id)}
                style={{
                  position: 'absolute',
                  top: 16,
                  right: 16,
                  zIndex: 10,
                  padding: 4,
                }}
              >
                {loadingBankIds.includes(bank.id) ? (
                  <ActivityIndicator size="small" color="#308a5a" />
                ) : (
                  <FontAwesome
                    name={bank.status == 1 ? 'toggle-on' : 'toggle-off'}
                    size={28}
                    color={bank.status == 1 ? '#2ecc71' : '#ccc'}
                  />
                )}
              </TouchableOpacity>
              
              <View style={styles.bankCardTop}>
                <Image
                  source={{ uri: `https://my.pay2s.vn/assets/media/banks/${bank.shortBankName.toLowerCase()}.jpg` }}
                  style={styles.bankCardLogo}
                />
              </View>

              <Text style={styles.bankCardName}>{bank.bankName}</Text>

              <View style={styles.detailRow}>
                <FontAwesome name="bank" size={14} color="#888" style={styles.detailIcon} />
                <Text style={styles.detailLabel}>Số tài khoản:</Text>
                <Text style={styles.detailValue} numberOfLines={1} ellipsizeMode="middle">
                  {bank.accountNumber}
                </Text>
              </View>

              <View style={styles.detailRow}>
                <FontAwesome name="tag" size={14} color="#888" style={styles.detailIcon} />
                <Text style={styles.detailLabel}>Loại tài khoản:</Text>
                <View style={[styles.badge, getBadgeStyle(bank.type)]}>
                  <Text style={styles.badgeText}>
                    {bank.type === 'openapi'
                      ? 'OpenAPI'
                      : bank.type === 'business'
                      ? 'Doanh nghiệp'
                      : 'Cá nhân'}
                  </Text>
                </View>
              </View>

              <View style={styles.detailRow}>
                <FontAwesome name="exchange" size={14} color="#888" style={styles.detailIcon} />
                <Text style={styles.detailLabel}>Số lượng giao dịch:</Text>
                <Text style={styles.detailValue} numberOfLines={1}>
                  {bank.transaction_count} giao dịch
                </Text>
              </View>

              <View style={styles.detailRow}>
                <FontAwesome name="money" size={14} color="#888" style={styles.detailIcon} />
                <Text style={styles.detailLabel}>Tổng tiền:</Text>
                <Text style={styles.detailValue} numberOfLines={1}>
                  {parseInt(bank.total_amount).toLocaleString()} ₫
                </Text>
              </View>

              <Text
                style={[
                  styles.bankCardStatus,
                  { color: bank.status == 1 ? '#2ecc71' : '#e74c3c' },
                ]}
              >
                ● {bank.statusText}
              </Text>
            </View>
          ))}
        </ScrollView>
      )}
    </View>
  );
};
