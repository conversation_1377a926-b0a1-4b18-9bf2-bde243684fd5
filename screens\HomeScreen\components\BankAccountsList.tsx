import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image, ActivityIndicator, Dimensions } from 'react-native';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import { styles } from '../HomeScreen.styles';
import { BankAccount } from '../hooks/useBankAccounts';

interface BankAccountsListProps {
  linkedBanks: BankAccount[];
  loadingBankIds: number[];
  onToggleBankStatus: (bankId: number) => void;
}

const BankCard: React.FC<{
  bank: BankAccount;
  index: number;
  isLoading: boolean;
  onToggle: (bankId: number) => void;
}> = ({ bank, index, isLoading, onToggle }) => {
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1,
        delay: index * 100,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        delay: index * 100,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const getBadgeGradient = (type: string) => {
    switch (type.toLowerCase()) {
      case 'openapi':
        return ['#ff9a56', '#ff6b35'];
      case 'business':
        return ['#56ab2f', '#a8e6cf'];
      default:
        return ['#667eea', '#764ba2'];
    }
  };

  const handleToggle = () => {
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
    onToggle(bank.id);
  };

  return (
    <Animated.View
      style={[
        {
          transform: [
            { scale: scaleAnim },
            { translateY: slideAnim }
          ]
        }
      ]}
    >
      <LinearGradient
        colors={bank.status === 1
          ? ['#ffffff', '#f8f9fa', '#ffffff']
          : ['#f5f5f5', '#e8e8e8', '#f0f0f0']
        }
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={[styles.bankCard, {
          borderWidth: bank.status === 1 ? 2 : 1,
          borderColor: bank.status === 1 ? '#4CAF50' : '#e0e0e0',
        }]}
      >
        {/* Status Glow Effect */}
        {bank.status === 1 && (
          <View style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: 4,
            backgroundColor: '#4CAF50',
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            shadowColor: '#4CAF50',
            shadowOffset: { width: 0, height: 0 },
            shadowOpacity: 0.6,
            shadowRadius: 8,
            elevation: 8,
          }} />
        )}

        <TouchableOpacity
          onPress={handleToggle}
          disabled={isLoading}
          style={styles.toggleButton}
          activeOpacity={0.7}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#667eea" />
          ) : (
            <View style={[
              styles.toggleSwitch,
              {
                backgroundColor: bank.status === 1 ? '#4CAF50' : '#ccc',
                shadowColor: bank.status === 1 ? '#4CAF50' : '#999',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: bank.status === 1 ? 0.4 : 0.2,
                shadowRadius: 4,
                elevation: 4,
              }
            ]}>
              <Animated.View style={[
                styles.toggleThumb,
                {
                  transform: [{
                    translateX: bank.status === 1 ? 20 : 2
                  }],
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.3,
                  shadowRadius: 3,
                  elevation: 5,
                }
              ]} />
            </View>
          )}
        </TouchableOpacity>

        <View style={styles.bankCardTop}>
          <View style={[styles.bankLogoContainer, {
            backgroundColor: '#ffffff',
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.15,
            shadowRadius: 8,
            elevation: 6,
          }]}>
            <Image
              source={{ uri: `https://my.pay2s.vn/assets/media/banks/${bank.shortBankName.toLowerCase()}.jpg` }}
              style={styles.bankCardLogo}
            />
          </View>
        </View>

        <Text style={[styles.bankCardName, {
          fontSize: 18,
          fontWeight: 'bold',
          color: '#2c3e50',
          textAlign: 'center',
          marginBottom: 16,
        }]}>{bank.bankName}</Text>

        <View style={[styles.detailRow, { marginBottom: 12 }]}>
          <FontAwesome name="bank" size={16} color="#667eea" style={styles.detailIcon} />
          <Text style={[styles.detailLabel, { fontSize: 14, fontWeight: '500' }]}>Số tài khoản:</Text>
          <Text style={[styles.detailValue, {
            fontSize: 14,
            fontWeight: '600',
            color: '#2c3e50',
          }]} numberOfLines={1} ellipsizeMode="middle">
            {bank.accountNumber}
          </Text>
        </View>

        <View style={[styles.detailRow, { marginBottom: 12 }]}>
          <FontAwesome name="tag" size={16} color="#667eea" style={styles.detailIcon} />
          <Text style={[styles.detailLabel, { fontSize: 14, fontWeight: '500' }]}>Loại tài khoản:</Text>
          <LinearGradient
            colors={getBadgeGradient(bank.type)}
            style={[styles.badge, {
              paddingHorizontal: 12,
              paddingVertical: 6,
              borderRadius: 16,
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.2,
              shadowRadius: 4,
              elevation: 3,
            }]}
          >
            <Text style={[styles.badgeText, {
              fontSize: 12,
              fontWeight: 'bold',
              textShadowColor: 'rgba(0, 0, 0, 0.3)',
              textShadowOffset: { width: 0, height: 1 },
              textShadowRadius: 2,
            }]}>
              {bank.type === 'openapi'
                ? 'OpenAPI'
                : bank.type === 'business'
                ? 'Doanh nghiệp'
                : 'Cá nhân'}
            </Text>
          </LinearGradient>
        </View>

        <View style={[styles.detailRow, { marginBottom: 12 }]}>
          <FontAwesome name="exchange" size={16} color="#667eea" style={styles.detailIcon} />
          <Text style={[styles.detailLabel, { fontSize: 14, fontWeight: '500' }]}>Số lượng giao dịch:</Text>
          <Text style={[styles.detailValue, {
            fontSize: 14,
            fontWeight: '600',
            color: '#2c3e50',
          }]} numberOfLines={1}>
            {bank.transaction_count} giao dịch
          </Text>
        </View>

        <View style={[styles.detailRow, { marginBottom: 16 }]}>
          <FontAwesome name="money" size={16} color="#667eea" style={styles.detailIcon} />
          <Text style={[styles.detailLabel, { fontSize: 14, fontWeight: '500' }]}>Tổng tiền:</Text>
          <Text style={[styles.detailValue, {
            fontSize: 16,
            fontWeight: 'bold',
            color: '#e74c3c',
          }]} numberOfLines={1}>
            {parseInt(bank.total_amount).toLocaleString()} ₫
          </Text>
        </View>

        <View style={[styles.statusContainer, {
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          paddingTop: 16,
          borderTopWidth: 1,
          borderTopColor: 'rgba(0, 0, 0, 0.1)',
        }]}>
          <View style={[
            styles.statusDot,
            {
              backgroundColor: bank.status === 1 ? '#4CAF50' : '#f44336',
              shadowColor: bank.status === 1 ? '#4CAF50' : '#f44336',
              shadowOffset: { width: 0, height: 0 },
              shadowOpacity: 0.6,
              shadowRadius: 4,
              elevation: 4,
            }
          ]} />
          <Text
            style={[
              styles.bankCardStatus,
              {
                color: bank.status === 1 ? '#4CAF50' : '#f44336',
                fontSize: 14,
                fontWeight: '600',
              },
            ]}
          >
            {bank.statusText}
          </Text>
        </View>
      </LinearGradient>
    </Animated.View>
  );
};

export const BankAccountsList: React.FC<BankAccountsListProps> = ({
  linkedBanks,
  loadingBankIds,
  onToggleBankStatus,
}) => {
  const screenWidth = Dimensions.get('window').width;

  const getBadgeStyle = (type: string) => {
    switch (type.toLowerCase()) {
      case 'openapi':
        return { backgroundColor: '#f39c12' };
      case 'business':
        return { backgroundColor: '#2ecc71' };
      default:
        return { backgroundColor: '#3498db' };
    }
  };

  return (
    <View style={styles.section}>
      <LinearGradient
        colors={['#4CAF50', '#45a049', '#66bb6a']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={[styles.sectionHeader, {
          shadowColor: '#4CAF50',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.3,
          shadowRadius: 8,
          elevation: 6,
        }]}
      >
        <FontAwesome name="credit-card" size={20} color="#ffffff" />
        <Text style={[styles.sectionHeaderText, {
          fontSize: 16,
          fontWeight: 'bold',
          textShadowColor: 'rgba(0, 0, 0, 0.3)',
          textShadowOffset: { width: 0, height: 1 },
          textShadowRadius: 2,
        }]}>Tài khoản ngân hàng đã liên kết</Text>
      </LinearGradient>
      
      {linkedBanks.length === 0 ? (
        <View style={[styles.emptyState, {
          alignItems: 'center',
          paddingVertical: 40,
          paddingHorizontal: 20,
        }]}>
          <FontAwesome name="bank" size={48} color="#ccc" />
          <Text style={[styles.emptyStateText, {
            fontSize: 16,
            color: '#666',
            fontWeight: '600',
            marginTop: 16,
          }]}>Chưa có tài khoản nào</Text>
          <Text style={[styles.emptyStateSubtext, {
            fontSize: 14,
            color: '#999',
            marginTop: 4,
            textAlign: 'center',
          }]}>Thêm tài khoản ngân hàng để bắt đầu</Text>
        </View>
      ) : (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ paddingHorizontal: 16, paddingVertical: 16 }}
          snapToInterval={screenWidth - 60}
          decelerationRate="fast"
          snapToAlignment="start"
        >
          {linkedBanks.map((bank, idx) => (
            <BankCard
              key={bank.id}
              bank={bank}
              index={idx}
              isLoading={loadingBankIds.includes(bank.id)}
              onToggle={onToggleBankStatus}
            />
          ))}
        </ScrollView>
      )}
    </View>
  );
};
