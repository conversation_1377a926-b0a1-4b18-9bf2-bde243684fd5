import React from 'react';
import { ScrollView, TouchableOpacity, Image, Text } from 'react-native';
import { styles } from '../HomeScreen.styles';
import { BlogPost } from '../hooks/useBlogPosts';

interface BlogPostsListProps {
  blogPosts: BlogPost[];
  onBlogPress: (url: string) => void;
}

export const BlogPostsList: React.FC<BlogPostsListProps> = ({
  blogPosts,
  onBlogPress,
}) => {
  return (
    <ScrollView horizontal showsHorizontalScrollIndicator={false}>
      {blogPosts.map((post) => (
        <TouchableOpacity
          key={post.id}
          style={styles.blogCard}
          onPress={() => {
            console.log('Đã bấm blog:', post.link);
            onBlogPress(post.link);
          }}
        >
          {post.image && (
            <Image
              source={{ uri: post.image }}
              style={{ width: 260, height: 140, borderRadius: 8 }}
            />
          )}
          <Text style={styles.blogTitle} numberOfLines={2}>
            {post.title}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );
};
