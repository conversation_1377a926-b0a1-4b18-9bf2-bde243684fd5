import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { StackNavigationProp } from '@react-navigation/stack';
import { useFocusEffect } from '@react-navigation/native';
import KeepAwake from 'react-native-keep-awake';
import PageLayout from '../../components/PageLayout';
import { RootStackParamList } from '../../types';
import { useNotifications } from '../../contexts/NotificationContext';

// Import hooks
import { useUserData } from './hooks/useUserData';
import { useBankAccounts } from './hooks/useBankAccounts';
import { useBlogPosts } from './hooks/useBlogPosts';

// Import components
import { UserCard } from './components/UserCard';
import { StatisticsCards } from './components/StatisticsCards';
import { BankAccountsList } from './components/BankAccountsList';
import { BlogPostsList } from './components/BlogPostsList';

// Import styles
import { styles } from './HomeScreen.styles';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Home'>;
type Props = {
  navigation: HomeScreenNavigationProp;
};

export default function HomeScreen({ navigation }: Props) {
  KeepAwake.activate();
  const { fetchNotifications } = useNotifications();

  // Custom hooks
  const { userData, loading: userLoading, fetchUserData } = useUserData();
  const { linkedBanks, loadingBankIds, fetchLinkedBanks, toggleBankStatus } = useBankAccounts();
  const { blogPosts, fetchBlogPosts } = useBlogPosts();

  // Local state
  const [refreshing, setRefreshing] = useState(false);

  // Handlers
  const handleBlogPress = (url: string) => {
    navigation.navigate('Webview', { url });
  };

  const handleRefresh = () => {
    setRefreshing(true);
    Promise.all([
      fetchUserData(),
      fetchLinkedBanks(),
      fetchBlogPosts(),
    ]).finally(() => {
      setRefreshing(false);
    });
  };

  const handleFetchData = useCallback(async () => {
    try {
      await Promise.all([
        fetchUserData(),
        fetchLinkedBanks(),
        fetchBlogPosts(),
        fetchNotifications(),
      ]);
    } catch (err: any) {
      // Error handling is done in useUserData hook
      await AsyncStorage.multiRemove(['userToken', 'userId']);
      navigation.navigate('Login');
    }
  }, [fetchUserData, fetchLinkedBanks, fetchBlogPosts, fetchNotifications, navigation]);

  useFocusEffect(
    useCallback(() => {
      handleFetchData();
    }, [handleFetchData])
  );

  if (userLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#308a5a" />
        <Text style={{ marginTop: 10 }}>Đang tải dữ liệu...</Text>
      </View>
    );
  }

  return (
    <PageLayout>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        <View style={styles.content}>
          {/* User Information Card */}
          <UserCard userData={userData} />

          {/* Statistics Cards */}
          <StatisticsCards userData={userData} />

          {/* Bank Accounts List */}
          <BankAccountsList
            linkedBanks={linkedBanks}
            loadingBankIds={loadingBankIds}
            onToggleBankStatus={toggleBankStatus}
          />

          {/* Blog Posts List */}
          <BlogPostsList
            blogPosts={blogPosts}
            onBlogPress={handleBlogPress}
          />
        </View>
      </ScrollView>
    </PageLayout>
  );
}
