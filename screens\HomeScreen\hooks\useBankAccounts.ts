import { useState, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import { Alert } from 'react-native';

export interface BankAccount {
  id: number;
  bankName: string;
  shortBankName: string;
  accountNumber: string;
  type: string;
  transaction_count: number;
  total_amount: string;
  status: number;
  statusText: string;
}

export const useBankAccounts = () => {
  const [linkedBanks, setLinkedBanks] = useState<BankAccount[]>([]);
  const [loadingBankIds, setLoadingBankIds] = useState<number[]>([]);

  const fetchLinkedBanks = useCallback(async () => {
    try {
      const [userToken, userId] = await Promise.all([
        AsyncStorage.getItem('userToken'),
        AsyncStorage.getItem('userId'),
      ]);
      if (!userToken || !userId) return;

      const res = await axios.post(
        'https://api.pay2s.vn/api/v1/bank',
        `action=bank_account&user_id=${userId}`,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Authorization: `Bearer ${userToken}`,
          },
        }
      );

      if (res.data.status && Array.isArray(res.data.banks)) {
        setLinkedBanks(res.data.banks);
      }
    } catch (err) {
      console.error('Lỗi khi lấy danh sách ngân hàng:', err);
    }
  }, []);

  const toggleBankStatus = async (bankId: number) => {
    try {
      setLoadingBankIds(prev => [...prev, bankId]);

      const [token, userId] = await Promise.all([
        AsyncStorage.getItem('userToken'),
        AsyncStorage.getItem('userId')
      ]);

      if (!token || !userId) return;

      const res = await axios.post(
        'https://api.pay2s.vn/api/v1/bank',
        `action=change_status&user_id=${userId}&bankId=${bankId}`,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Authorization: `Bearer ${token}`
          }
        }
      );

      if (res.data.status) {
        setLinkedBanks(prev =>
          prev.map(bank =>
            bank.id === bankId
              ? {
                  ...bank,
                  status: bank.status === 1 ? 0 : 1,
                  statusText: bank.status === 1 ? 'Đã tắt' : 'Đang hoạt động'
                }
              : bank
          )
        );
      } else {
        Alert.alert('Lỗi', res.data.message || 'Không thể thay đổi trạng thái ngân hàng');
      }
    } catch (err) {
      console.error('Lỗi toggleBankStatus:', err);
      Alert.alert('Lỗi', 'Không thể thay đổi trạng thái ngân hàng');
    } finally {
      setLoadingBankIds(prev => prev.filter(id => id !== bankId));
    }
  };

  return {
    linkedBanks,
    loadingBankIds,
    fetchLinkedBanks,
    toggleBankStatus,
  };
};
