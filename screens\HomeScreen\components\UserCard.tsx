import React, { useState, useEffect } from 'react';
import { View, Text, Image, Animated } from 'react-native';
import { styles } from '../HomeScreen.styles';
import { UserData } from '../hooks/useUserData';

interface UserCardProps {
  userData: UserData;
}

export const UserCard: React.FC<UserCardProps> = ({ userData }) => {
  const [typedText, setTypedText] = useState('');
  const [fullText, setFullText] = useState('');
  const [blinkAnim] = useState(new Animated.Value(1));

  const getGreetingByTime = () => {
    const hour = new Date().getHours();
    if (hour >= 5 && hour < 11) return 'Chào buổi sáng,';
    if (hour >= 11 && hour < 18) return 'Chào buổi chiều,';
    if (hour >= 18 && hour < 22) return 'Chào buổi tối,';
    return '<PERSON><PERSON><PERSON> bạn ngủ ngon,';
  };

  useEffect(() => {
    setFullText(getGreetingByTime());
    Animated.loop(
      Animated.sequence([
        Animated.timing(blinkAnim, {
          toValue: 0,
          duration: 700,
          useNativeDriver: true,
        }),
        Animated.timing(blinkAnim, {
          toValue: 1,
          duration: 700,
          useNativeDriver: true,
        })
      ])
    ).start();
  }, []);

  useEffect(() => {
    if (!fullText) return;
    let index = 0;
    let typingInterval: NodeJS.Timeout;
    let restartTimeout: NodeJS.Timeout;

    const startTyping = () => {
      setTypedText('');
      typingInterval = setInterval(() => {
        setTypedText(prev => {
          if (index < fullText.length) {
            const nextChar = fullText.charAt(index);
            index++;
            return prev + nextChar;
          } else {
            clearInterval(typingInterval);
            restartTimeout = setTimeout(() => {
              index = 0;
              startTyping();
            }, 5000);
            return prev;
          }
        });
      }, 100);
    };

    startTyping();
    return () => {
      clearInterval(typingInterval);
      clearTimeout(restartTimeout);
    };
  }, [fullText]);

  return (
    <View style={styles.userCard}>
      <View style={styles.userCardTop}>
        <View>
          <Text style={styles.welcome}>{typedText}</Text>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style={styles.fullname}>{userData.name}</Text>
            <Animated.View
              style={{
                width: 8,
                height: 8,
                borderRadius: 4,
                backgroundColor: '#2ecc71',
                marginLeft: 6,
                marginRight: 4,
                opacity: blinkAnim,
              }}
            />
            <Text style={styles.online}>Online</Text>
          </View>

          {userData.companyName ? (
            <>
              <View style={styles.companyBadge}>
                <Text style={styles.companyText}>{userData.companyName}</Text>
              </View>
              <Text style={styles.planText}>
                Gói sử dụng: <Text style={styles.planName}>{userData.currentPlan}</Text>
              </Text>
            </>
          ) : (
            <>
              <View style={styles.companyBadge}>
                <Text style={styles.companyText}>{userData.username}</Text>
              </View>
              <Text style={styles.planText}>
                Gói sử dụng: <Text style={styles.planName}>{userData.currentPlan}</Text>
              </Text>
            </>
          )}
        </View>

        <Image
          source={{ uri: 'https://my.pay2s.vn/assets/media/avatars/waving-hand.svg' }}
          style={styles.userAvatar}
        />
      </View>
    </View>
  );
};
